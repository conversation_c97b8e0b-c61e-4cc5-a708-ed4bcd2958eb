import math

from odoo import api, fields, models
from odoo.tools.float_utils import float_compare, float_is_zero


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    qty_double_uom = fields.Float(
        string="Q.D.U",
        help="Quantity for double unit of measure",
        digits="Double Unit of Measure",
        compute="_compute_qty_double_uom",
        inverse="_inverse_qty_double_uom",
        store=True,
    )
    double_uom_id = fields.Many2one(
        "uom.uom",
        string="D.U",
        help="Double unit of measure",
    )
    qdu_received = fields.Float(
        string="Q.D.U Received",
        help="Quantity received for double unit of measure",
        digits="Double Unit of Measure",
        compute="_compute_qdu_received",
        compute_sudo=True,
        store=True,
    )
    qdu_invoiced = fields.Float(
        string="Q.D.U Invoiced",
        help="Quantity invoiced for double unit of measure",
        digits="Double Unit of Measure",
        compute="_compute_qdu_invoiced",
        store=True,
    )
    coefficient = fields.Float(
        help="This coefficient is used to convert double units"
        " into a single unit of measurement.",
        digits="Double Unit of Measure",
        readonly=True,
    )
    has_different_uom_categ = fields.Boolean(
        compute="_compute_has_different_uom_categ", store=True
    )
    is_product_double_uom = fields.Boolean(
        compute="_compute_is_product_double_uom", store=True
    )
    qdu_to_invoice = fields.Float(
        string="Q.D.U To Invoice",
        compute="_compute_qdu_invoiced",
        help="Quantity to invoice for double unit of measure",
        store=True,
        readonly=True,
        digits="Double Unit of Measure",
    )
    edit_qty = fields.Boolean(
        "Edit QTY",
        help="Enable to manually edit the Q.D.U. If disabled, the quantity is "
        "automatically calculated based on the coefficient and the "
        "product's unit of measure settings.",
    )

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    @api.depends(
        "product_qty",
        "product_id",
        "product_id.is_double_uom",
        "double_uom_id",
        "edit_qty",
        "product_uom",
    )
    def _compute_qty_double_uom(self):
        """
        Computes the value of `qty_double_uom` based on product quantity
        and double UoM settings.

        This method:
        - Calculates the `qty_double_uom` using the product's base UoM,
        conversion coefficient, and double UoM configuration.
        - Handles edge cases such as editable quantities, zero initial
        values, or specific UoM setups.
        - Rounds the value for unit-based UoMs when `edit_qty` is enabled.
        """
        unit_category = self.env.ref("uom.product_uom_categ_unit")
        for record in self:
            if (
                record.product_id.is_double_uom
                and record.coefficient
                and (
                    record.edit_qty
                    or record.qty_double_uom == 0
                    or record.product_id.double_uom_id == record.product_id.uom_po_id
                )
            ):
                qty = (
                    record.product_uom._compute_quantity(
                        record.product_qty, record.product_id.uom_po_id
                    )
                    / record.coefficient
                    if record.product_id.double_uom_id != record.product_id.uom_po_id
                    else record.product_qty * record.coefficient
                )
                record.qty_double_uom = (
                    math.ceil(qty)
                    if record.double_uom_id.category_id == unit_category
                    and record.edit_qty
                    else qty
                )

    def _compute_product_qty(self):
        """
        Set the coefficient of the product's double UOM as the suggested
        quantity when selecting a product in the purchase order line"""
        for record in self:
            if (
                record.product_id.is_double_uom
                and not record.product_packaging_id
                and not record.edit_qty
            ):
                record.product_qty = record.coefficient
        return super()._compute_product_qty()

    @api.depends(
        "double_uom_id.category_id",
        "product_uom.category_id",
        "product_id.is_double_uom",
    )
    def _compute_has_different_uom_categ(self):
        for record in self:
            if record.product_id.is_double_uom:
                record.has_different_uom_categ = bool(
                    record.double_uom_id.category_id != record.product_uom.category_id
                )
            else:
                record.has_different_uom_categ = False

    @api.depends("product_id", "product_id.is_double_uom")
    def _compute_is_product_double_uom(self):
        for rec in self:
            if rec.product_id and rec.product_id.is_double_uom:
                rec.is_product_double_uom = True
            else:
                rec.is_product_double_uom = False

    @api.depends("move_ids.state", "move_ids.qdu_done", "move_ids.double_uom_id")
    def _compute_qdu_received(self):
        for line in self:
            if line.qty_received_method == "stock_moves":
                total = 0.0
                # In case of a BOM in kit, the products delivered do not
                # correspond to the products in  the PO. Therefore, we can
                # skip them since they will be handled later on.
                for move in line._get_po_line_moves():
                    quantity = (
                        move.qdu_done
                        if move.double_uom_id.category_id
                        != move.product_uom.category_id
                        else move.quantity
                    )
                    if move.state == "done":
                        if move._is_purchase_return():
                            if move.to_refund:
                                total -= quantity
                        elif (
                            move.origin_returned_move_id
                            and move.origin_returned_move_id._is_dropshipped()
                            and not move._is_dropshipped_returned()
                        ):
                            # Edge case: the dropship is returned to the stock,
                            # no to the supplier.
                            # In this case, the qdu received on the PO is
                            # set although we didn't
                            # receive the product physically in our stock.
                            # To avoid counting the quantity twice,
                            # we do nothing.
                            pass
                        elif (
                            move.origin_returned_move_id
                            and move.origin_returned_move_id._is_purchase_return()
                            and not move.to_refund
                        ):
                            pass
                        else:
                            total += quantity
                line.qdu_received = total

    @api.depends(
        "invoice_lines.move_id.state",
        "invoice_lines.qty_double_uom",
        "qty_received",
        "qty_double_uom",
        "order_id.state",
    )
    def _compute_qdu_invoiced(self):
        for record in self:
            # compute qdu_invoiced
            qty = 0.0
            for inv_line in record._get_invoice_lines():
                if (
                    inv_line.move_id.state not in ["cancel"]
                    or inv_line.move_id.payment_state == "invoicing_legacy"
                ):
                    if inv_line.move_id.move_type == "in_invoice":
                        qty += inv_line.qty_double_uom
                    elif inv_line.move_id.move_type == "in_refund":
                        qty -= inv_line.qty_double_uom
            record.qdu_invoiced = qty

            # compute qdu_to_invoice
            if record.order_id.state in ["purchase", "done"]:
                if record.product_id.purchase_method == "purchase":
                    record.qdu_to_invoice = record.qty_double_uom - record.qdu_invoiced
                else:
                    record.qdu_to_invoice = record.qdu_received - record.qdu_invoiced
            else:
                record.qdu_to_invoice = 0

    def _prepare_base_line_for_taxes_computation(self, **kwargs):
        """Inherit this method to add qty_double_uom to res."""
        res = super()._prepare_base_line_for_taxes_computation(**kwargs)
        res.update(qty_double_uom=self.qty_double_uom)
        return res

    # -------------------------------------------------------------------------
    # INVERSE METHODS
    # -------------------------------------------------------------------------

    @api.onchange("qty_double_uom", "edit_qty")
    def _inverse_qty_double_uom(self):
        """
        Synchronizes `product_qty` based on changes to `qty_double_uom` and `edit_qty`.

        This method:
        - Updates the `product_qty` field when `qty_double_uom` or `edit_qty`
        is modified.
        - Calculates the `product_qty` based on the `qty_double_uom` and
        the product's UoM, using the conversion coefficient.
        - Applies the calculation only if the product has different UoM categories
         (`has_different_uom_categ`) and `edit_qty` is not enabled.
        """
        for record in self:
            if record.has_different_uom_categ and not record.edit_qty:
                record.product_qty = record.product_id.uom_po_id._compute_quantity(
                    record.qty_double_uom * record.coefficient, record.product_uom
                )

    # -------------------------------------------------------------------------
    # ONCHANGE METHODS
    # -------------------------------------------------------------------------

    def _product_id_change(self):
        res = super()._product_id_change()
        if self.product_id.is_double_uom:
            self.double_uom_id = (
                self.product_id.double_uom_id.id
                if self.product_id.double_uom_id != self.product_id.uom_po_id
                else self.product_id.uom_id.id
            )
            self.coefficient = self.product_id.coefficient
        return res

    def _suggest_quantity(self):
        res = super()._suggest_quantity()
        # Set the coefficient of the product's double UOM as the suggested
        # quantity when selecting a product in the purchase order line
        if self.product_id.is_double_uom and self.has_different_uom_categ:
            self.product_qty = self.coefficient
        return res

    # -------------------------------------------------------------------------
    # BUSINESS METHODS
    # -------------------------------------------------------------------------

    def _prepare_account_move_line(self, move=False):
        self.ensure_one()
        res = super()._prepare_account_move_line(move)
        res.update(
            {
                "qty_double_uom": self.qdu_to_invoice,
                "double_uom_id": (
                    self.product_id.double_uom_id.id
                    if self.product_id.double_uom_id != self.product_id.uom_po_id
                    else self.product_id.uom_id.id
                ),
                "coefficient": self.coefficient,
            }
        )
        return res

    def _prepare_purchase_order_line(
        self, product_id, product_qty, product_uom, company_id, supplier, po
    ):
        res = super()._prepare_purchase_order_line(
            product_id, product_qty, product_uom, company_id, supplier, po
        )
        if product_id.is_double_uom:
            res["double_uom_id"] = (
                product_id.double_uom_id.id
                if product_id.double_uom_id != product_id.uom_po_id
                else product_id.uom_id.id
            )
            res["coefficient"] = product_id.coefficient
        return res

    def _get_qdu_procurement(self):
        self.ensure_one()
        qty = 0.0
        outgoing_moves, incoming_moves = self._get_outgoing_incoming_moves()
        for move in outgoing_moves:
            qty_to_compute = move.qdu_done if move.state == "done" else move.qdu_demand
            qty -= qty_to_compute
        for move in incoming_moves:
            qty_to_compute = move.qdu_done if move.state == "done" else move.qdu_demand
            qty += qty_to_compute
        return qty

    def _get_move_dests_initial_qdu_demand(self, move_dests):
        # return sum of dqu_demand in stock.move
        return sum(
            move_dests.filtered(
                lambda m: m.state != "cancel" and m.location_dest_id.usage != "supplier"
            ).mapped("qdu_demand")
        )

    def _prepare_stock_moves(self, picking):
        """
        Prepare the stock move values for the given picking.
        Inherit the base `_prepare_stock_moves` method to handle additional
        logic for products with double units of measure.
        Ensures the correct quantities and price units are set for stock moves
        and handles different scenarios based on the state of destination moves.
        """
        res = super()._prepare_stock_moves(picking)
        # Start patch: we are going to return a new res to handle additional
        # logic for products with double units of measure.
        self.ensure_one()
        new_res = []

        # In case the module stock_dropshipping is installed, we must get
        # the value of sale_line_id from res and add it to our new_res to
        # avoid overriding the standard
        if "sale_line_id" in res:
            new_res.update({"sale_line_id": res["sale_line_id"]})

        # Return an empty list if the type of product
        # is neither 'product' nor 'cons
        if self.product_id.type not in ["product", "consu"]:
            return new_res

        # Return new_res list following the initial method
        # of odoo and update it to notion of double uom
        price_unit = self._get_stock_move_price_unit()
        qty = self._get_qty_procurement()
        qdu = self._get_qdu_procurement()

        move_dests = self.move_dest_ids or self.move_ids.move_dest_ids
        move_dests = move_dests.filtered(
            lambda m: m.state != "cancel" and not m._is_purchase_return()
        )
        qdu_to_attach = 0
        if not move_dests:
            qty_to_attach = 0
            qty_to_push = self.product_qty - qty
            qdu_to_push = (
                (
                    self.qty_double_uom - qdu
                    if (self.double_uom_id.category_id != self.product_uom.category_id)
                    else ((self.product_uom_qty - qdu) / self.coefficient)
                )
                if self.product_id.is_double_uom
                else 0
            )

        else:
            # compute qty to attach and push of quantity
            move_dests_initial_demand = self._get_move_dests_initial_demand(move_dests)
            qty_to_attach = move_dests_initial_demand - qty
            qty_to_push = self.product_qty - move_dests_initial_demand
            qdu_to_push = 0
            # compute qty to attach and push of qdu
            if self.product_id.is_double_uom:
                move_dests_initial_qdu_demand = self._get_move_dests_initial_qdu_demand(
                    move_dests
                )
                qdu_to_attach = move_dests_initial_qdu_demand - qdu
                if self.double_uom_id.category_id == self.product_uom.category_id:
                    qdu_to_push = (
                        self.double_uom_id._compute_quantity(
                            self.qty_double_uom,
                            self.product_uom,
                            rounding_method="HALF-UP",
                        )
                        - move_dests_initial_qdu_demand
                    )
                else:
                    qdu_to_push = self.qty_double_uom - move_dests_initial_qdu_demand

        if (
            float_compare(
                qty_to_attach, 0.0, precision_rounding=self.product_uom.rounding
            )
            > 0
        ):
            product_uom_qty, product_uom = self.product_uom._adjust_uom_quantities(
                qty_to_attach, self.product_id.uom_id
            )
            new_res.append(
                self.with_context(qdu=qdu_to_attach)._prepare_stock_move_vals(
                    picking, price_unit, product_uom_qty, product_uom
                )
            )
        if not float_is_zero(qty_to_push, precision_rounding=self.product_uom.rounding):
            product_uom_qty, product_uom = self.product_uom._adjust_uom_quantities(
                qty_to_push, self.product_id.uom_id
            )
            extra_move_vals = self.with_context(
                qdu=qdu_to_push
            )._prepare_stock_move_vals(
                picking, price_unit, product_uom_qty, product_uom
            )
            extra_move_vals["move_dest_ids"] = False  # don't attach
            new_res.append(extra_move_vals)
        # End patch
        return new_res

    def _prepare_stock_move_vals(
        self, picking, price_unit, product_uom_qty, product_uom
    ):
        """
            Prepare the values for a stock move.
        Extends the base `_prepare_stock_move_vals` method to include
        additional fields for products with double units of measure.
        If the context includes `qdu`, it sets the `qdu_done` and
        qdu_demand` fields in the result.
        """
        res = super()._prepare_stock_move_vals(
            picking, price_unit, product_uom_qty, product_uom
        )
        qdu = self._context.get("qdu", False)
        if qdu:
            if self.product_id.is_double_uom:
                res["qdu_done"] = qdu
                res["qdu_demand"] = qdu
        return res
